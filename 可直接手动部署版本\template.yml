# 此 AWS SAM 模板是根据您的函数配置生成的。如果您的函数有一个或多个触发器，则请注意与这些触发器关联的 AWS 资源未在此模板中完全指定，并且 AWS
# 资源包含占位符值。在 AWS 基础架构编辑器或您最喜欢的 IDE 中打开此模板并对其进行修改，以指定具有其他 AWS 资源的无服务器应用程序。
AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: An AWS Serverless Application Model template describing your function.
Resources:
  ttsproxy:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: s3://cf-templates-1a9ravqwjbug-us-west-2/tts-proxy.zip
      Description: ''
      MemorySize: 256
      Timeout: 180
      Handler: index.handler
      Runtime: nodejs22.x
      Architectures:
        - x86_64
      EphemeralStorage:
        Size: 512
      Environment:
        Variables:
          NODE_ENV: production
          PROXY_SECRET: AKIDFORI0ZwAFKMH1c6VjkbFk183pSs66xd9
          RATE_LIMIT_TABLE: tts-rate-limits
      EventInvokeConfig:
        MaximumEventAgeInSeconds: 21600
        MaximumRetryAttempts: 2
      PackageType: Zip
      Policies:
        - Statement:
            - Effect: Allow
              Action:
                - logs:CreateLogGroup
              Resource: arn:aws:logs:ap-northeast-3:515966520954:*
            - Effect: Allow
              Action:
                - logs:CreateLogStream
                - logs:PutLogEvents
              Resource:
                - >-
                  arn:aws:logs:ap-northeast-3:515966520954:log-group:/aws/lambda/tts-proxy:*
            - Effect: Allow
              Action:
                - polly:*
              Resource:
                - '*'
            - Action:
                - dynamodb:*
                - dax:*
                - application-autoscaling:DeleteScalingPolicy
                - application-autoscaling:DeregisterScalableTarget
                - application-autoscaling:DescribeScalableTargets
                - application-autoscaling:DescribeScalingActivities
                - application-autoscaling:DescribeScalingPolicies
                - application-autoscaling:PutScalingPolicy
                - application-autoscaling:RegisterScalableTarget
                - cloudwatch:DeleteAlarms
                - cloudwatch:DescribeAlarmHistory
                - cloudwatch:DescribeAlarms
                - cloudwatch:DescribeAlarmsForMetric
                - cloudwatch:GetMetricStatistics
                - cloudwatch:ListMetrics
                - cloudwatch:PutMetricAlarm
                - cloudwatch:GetMetricData
                - datapipeline:ActivatePipeline
                - datapipeline:CreatePipeline
                - datapipeline:DeletePipeline
                - datapipeline:DescribeObjects
                - datapipeline:DescribePipelines
                - datapipeline:GetPipelineDefinition
                - datapipeline:ListPipelines
                - datapipeline:PutPipelineDefinition
                - datapipeline:QueryObjects
                - ec2:DescribeVpcs
                - ec2:DescribeSubnets
                - ec2:DescribeSecurityGroups
                - iam:GetRole
                - iam:ListRoles
                - kms:DescribeKey
                - kms:ListAliases
                - sns:CreateTopic
                - sns:DeleteTopic
                - sns:ListSubscriptions
                - sns:ListSubscriptionsByTopic
                - sns:ListTopics
                - sns:Subscribe
                - sns:Unsubscribe
                - sns:SetTopicAttributes
                - lambda:CreateFunction
                - lambda:ListFunctions
                - lambda:ListEventSourceMappings
                - lambda:CreateEventSourceMapping
                - lambda:DeleteEventSourceMapping
                - lambda:GetFunctionConfiguration
                - lambda:DeleteFunction
                - resource-groups:ListGroups
                - resource-groups:ListGroupResources
                - resource-groups:GetGroup
                - resource-groups:GetGroupQuery
                - resource-groups:DeleteGroup
                - resource-groups:CreateGroup
                - tag:GetResources
                - kinesis:ListStreams
                - kinesis:DescribeStream
                - kinesis:DescribeStreamSummary
              Effect: Allow
              Resource: '*'
            - Action:
                - cloudwatch:GetInsightRuleReport
              Effect: Allow
              Resource: arn:aws:cloudwatch:*:*:insight-rule/DynamoDBContributorInsights*
            - Action:
                - iam:PassRole
              Effect: Allow
              Resource: '*'
              Condition:
                StringLike:
                  iam:PassedToService:
                    - application-autoscaling.amazonaws.com
                    - application-autoscaling.amazonaws.com.cn
                    - dax.amazonaws.com
            - Effect: Allow
              Action:
                - iam:CreateServiceLinkedRole
              Resource: '*'
              Condition:
                StringEquals:
                  iam:AWSServiceName:
                    - replication.dynamodb.amazonaws.com
                    - dax.amazonaws.com
                    - dynamodb.application-autoscaling.amazonaws.com
                    - contributorinsights.dynamodb.amazonaws.com
                    - kinesisreplication.dynamodb.amazonaws.com
      RecursiveLoop: Terminate
      SnapStart:
        ApplyOn: None
      Events:
        Api1:
          Type: HttpApi
          Properties:
            Path: /api/v1/text-to-speech/{voice_id}
            Method: POST
        Api2:
          Type: HttpApi
          Properties:
            Path: /api/v1/health
            Method: GET
      RuntimeManagementConfig:
        UpdateRuntimeOn: Auto
